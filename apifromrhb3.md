RHB Reflex API POSLIST v2.5 Summary

📌 General Notes

All JSON elements must be lowercase.

HMAC SHA256 is used for security validation across APIs.

Most APIs require a JWT token with HMAC header signature for authentication.

🔐 1. QR Request Token API
Method: POST Content-Type: application/x-www-form-urlencoded Auth: Basic (Username: posClient, Password: RSA encrypted) Endpoint:

UAT: https://uatreflex.rhbgroup.com/qrmerchant-auth/oauth/token

PROD: https://reflex.rhbgroup.com/qrmerchant-auth/oauth/token Requires Session: Yes Request Parameters:

grant_type: 'password' or 'refresh_token'

username, password: provided by RHB

nonce: for decrypting password

refresh_token: if refreshing Response Parameters:

access_token, token_type, refresh_token, expires_in, scope, timestamp, jti

On failure: ErrorResponse object with status, datetime, message, debugMessage

🖼️ 2. QR Image Generation API
Method: POST Content-Type: application/json Auth: Bearer + JWT token Endpoint:

UAT: https://uatreflex.rhbgroup.com/qrpresentment/generatePaynet/qrImage

PROD: https://reflex.rhbgroup.com/qrpresentment/generatePaynet/qrImage Requires Session: Yes Request Headers: Bearer token, HMAC (signed with jti) Body Parameters:

merchantCode, userId

Optional: transactionAmount, billNumber, transactionReference Response:

qrCode: base64 PNG image string (default 300x300 px)

HMAC validation and optional error object included

📋 3. QR Transaction Status Inquiry API
Method: GET Content-Type: application/json Auth: Bearer JWT Endpoint:

UAT: https://uatreflex.rhbgroup.com/qrmerchant-transaction/transaction/statusInquiry?...

PROD: https://reflex.rhbgroup.com/qrmerchant-transaction/transaction/statusInquiry?... Requires Session: Yes Query Options:

Search by combinations of billNumber, referenceNo, userId Response:

Transaction info: billNumber, paymentStatus, merchantCode, userId, outletCode, amount, referenceNo, dateTime, transactionReference

On error: ErrorResponse

🔄 4. SP POS Request Token API (Reflex requests from POS Provider)
Method: POST Content-Type: application/x-www-form-urlencoded Auth: Basic (Client ID + Password RSA encrypted) Request Parameters:

grant_type: 'password' or 'refresh_token'

Optional: username, password (RSA encrypted), nonce, refresh_token Response:

Same as QR Request Token

💳 5. SP POS Payment Confirmation API (Reflex to POS Provider)
Method: POST Content-Type: application/json Auth: Bearer JWT Headers: HMAC signed Body Parameters:

merchantCode, userId, billNumber, outletCode, transactionAmount, referenceNo, dateTime Response:

status, dateTime

Optional: debugMessage

🔒 HMAC SHA256 Usage (Request & Response)
Key: jti from JWT

Request value to digest: Bearer <JWT> + request JSON

Response value: Bearer <JWT> + response JSON + HTTP status

🛠 Tools:

Inspect JWT: https://jwt.io/

Generate HMAC: https://codebeautify.org/hmac-generator using SHA256