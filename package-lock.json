{"name": "doktedobi", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "doktedobi", "version": "1.0.0", "license": "ISC", "dependencies": {"crypto": "^1.0.1", "node-fetch": "^2.7.0", "node-rsa": "^1.1.1"}}, "node_modules/asn1": {"version": "0.2.6", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/crypto": {"version": "1.0.1", "license": "ISC"}, "node_modules/node-fetch": {"version": "2.7.0", "license": "MIT", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-rsa": {"version": "1.1.1", "license": "MIT", "dependencies": {"asn1": "^0.2.4"}}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/tr46": {"version": "0.0.3", "license": "MIT"}, "node_modules/webidl-conversions": {"version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/whatwg-url": {"version": "5.0.0", "license": "MIT", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}}}