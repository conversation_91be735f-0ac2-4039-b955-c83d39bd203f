This sheet contains the required credentials for authenticating against the RHB Reflex UAT (User Acceptance Testing) system.

🔐 Authentication Details (OAuth2.0)
Parameter	Value	Notes
Oauth Username	UATREFLEXPOS_SP	Service provider login
Oauth Password	RSA Encrypted string	Must be decrypted or signed
Nonce	b2af379f05eb4474b367373c0c812c12	Used for extra token security
Oauth Client ID	User10Client	Identifies the SP app

These are required when calling the token generation API.

🌐 Sheet 2: URL – API Endpoints & Flow
This sheet lists 3 key APIs used for POS QR-based payment integration.

#	API Purpose	Method	Encryption	Request By	Response By	UAT URL	Prod URL
1	QR Token Request	POST	HMAC	SP	Reflex	https://uatreflex.rhbgroup.com/qrmerchant-auth/oauth/token	✅
2	QR Image Request	POST	HMAC	SP	Reflex	https://uatreflex.rhbgroup.com/qrpresentment/generateQrImage	✅
3	QR Transaction Inquiry	GET	HMAC	SP	Reflex	https://uatreflex.rhbgroup.com/qrmerchant-transaction/queryTxn	✅

📌 API Summary with Flow
1. QR Token Request API
URL: /qrmerchant-auth/oauth/token

Method: POST

Use: Get JWT access token using OAuth credentials.

Security: RSA encryption for password, HMAC signature for API call.

Required Parameters:

oauth_username

oauth_password (RSA encrypted)

nonce

client_id

2. QR Image Request API
URL: /qrpresentment/generateQrImage

Method: POST

Use: Generate base64-encoded QR code image (static or dynamic).

Security: HMAC-authenticated, JWT token required.

Required Headers/Token:

Bearer JWT token from previous step

Request Body Example:

merchantId

cashierUserId

transactionAmount

billNumber

transactionReference

Response:

Base64 QR image

Transaction Reference ID

3. QR Transaction Inquiry API
URL: /qrmerchant-transaction/queryTxn

Method: GET

Use: Inquire about payment status for a given QR transaction.

Security: HMAC-authenticated, JWT token required.

Query Parameters:

billNumber

referenceNo

cashierUserId

Response:

paymentStatus (Found / Not Found)

referenceNo, creditingDateTime, amount, etc.

🔐 Security Measures Summary
RSA encryption: Used for passwords

HMAC: Used to verify API integrity and origin

JWT Token: Required for all transactional APIs

Nonce: Prevents replay attacks

✅ System Requirements
Item	Details
Environment	UAT / Production
Authentication	OAuth2 with encrypted credentials
Token Security	JWT + Nonce
Encryption	RSA for password, HMAC for payload
Request Format	JSON POST or GET with query params
Response Format	JSON (includes status, image, etc.)